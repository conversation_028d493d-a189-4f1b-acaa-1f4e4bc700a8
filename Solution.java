class ListNode {
    int val;
    ListNode next = null;
    public ListNode(int val) {
        this.val = val;
    }
}

 
public class Solution {
    /**
     * Note: 类名、方法名、参数名已经指定，请勿修改
     *
     *
     * 
     * @param listNode1 ListNode类  
     * @param listNode2 ListNode类  
     * @param startIndex int整型  
     * @param endIndex int整型  
     * @return int整型
     */
    public int execute(ListNode listNode1, ListNode listNode2, int startIndex, int endIndex) {
        // 初始化指针和变量
        ListNode p1 = listNode1;
        ListNode p2 = listNode2;
        int currentIndex = 1; // 位置从1开始
        int sum = 0; // 结果和

        // 合并两个有序链表并计算指定范围内的和
        while (p1 != null && p2 != null) {
            int currentValue;

            // 比较两个节点的值，选择较小的
            if (p1.val <= p2.val) {
                currentValue = p1.val;
                p1 = p1.next;
            } else {
                currentValue = p2.val;
                p2 = p2.next;
            }

            // 检查当前位置是否在指定范围内
            if (currentIndex >= startIndex && currentIndex <= endIndex) {
                sum += currentValue;
            }

            currentIndex++;

            // 如果已经超过endIndex，可以提前结束
            if (currentIndex > endIndex) {
                break;
            }
        }

        // 处理链表1的剩余节点
        while (p1 != null && currentIndex <= endIndex) {
            if (currentIndex >= startIndex) {
                sum += p1.val;
            }
            p1 = p1.next;
            currentIndex++;
        }

        // 处理链表2的剩余节点
        while (p2 != null && currentIndex <= endIndex) {
            if (currentIndex >= startIndex) {
                sum += p2.val;
            }
            p2 = p2.next;
            currentIndex++;
        }

        return sum;
    }
}
